import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { BarChart3, Shield, Users, Database } from "lucide-react";

export const Header = () => {
  return (
    <header className="border-b bg-gradient-secondary shadow-elevation">
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-gradient-primary p-2 rounded-lg">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-primary">PulseIQ</h1>
              <p className="text-xs text-muted-foreground">Data Intelligence Hub</p>
            </div>
          </div>
          
          <nav className="hidden md:flex items-center space-x-6">
            <Button variant="ghost" className="text-foreground hover:text-primary">
              <Shield className="h-4 w-4 mr-2" />
              IRA Portal
            </Button>
            <Button variant="ghost" className="text-foreground hover:text-primary">
              <Users className="h-4 w-4 mr-2" />
              Insurers
            </Button>
            <Button variant="ghost" className="text-foreground hover:text-primary">
              <Database className="h-4 w-4 mr-2" />
              Public Access
            </Button>
          </nav>

          <div className="flex items-center space-x-3">
            <Badge variant="outline" className="text-success border-success">
              Live
            </Badge>
            <Button className="bg-gradient-primary hover:bg-primary-glow text-white">
              Login
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};