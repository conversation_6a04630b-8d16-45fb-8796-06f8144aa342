import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { BarChart3, LogOut, Shield, Building2, Users } from 'lucide-react';
import IRADashboard from './dashboards/IRADashboard';
import InsurerDashboard from './dashboards/InsurerDashboard';
import PublicDashboard from './dashboards/PublicDashboard';

const Dashboard = () => {
  const { user, logout } = useAuth();

  if (!user) {
    return null;
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'ira':
        return <Shield className="h-5 w-5" />;
      case 'insurer':
        return <Building2 className="h-5 w-5" />;
      case 'public':
        return <Users className="h-5 w-5" />;
      default:
        return null;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ira':
        return 'bg-primary text-primary-foreground';
      case 'insurer':
        return 'bg-info text-white';
      case 'public':
        return 'bg-success text-white';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  const getRoleName = (role: string) => {
    switch (role) {
      case 'ira':
        return 'IRA Regulator';
      case 'insurer':
        return 'Insurance Company';
      case 'public':
        return 'Public User';
      default:
        return 'User';
    }
  };

  const renderDashboard = () => {
    switch (user.role) {
      case 'ira':
        return <IRADashboard />;
      case 'insurer':
        return <InsurerDashboard />;
      case 'public':
        return <PublicDashboard />;
      default:
        return <div>Dashboard not available for this role</div>;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-secondary">
      {/* Header */}
      <header className="border-b bg-card shadow-elevation">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-primary p-2 rounded-lg">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-primary">PulseIQ</h1>
                <p className="text-xs text-muted-foreground">Data Intelligence Hub</p>
              </div>
            </div>
            
            {/* User Info */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <Avatar>
                  <AvatarFallback className={getRoleColor(user.role)}>
                    {getRoleIcon(user.role)}
                  </AvatarFallback>
                </Avatar>
                <div className="text-right">
                  <p className="font-medium text-foreground">{user.name}</p>
                  <div className="flex items-center space-x-2">
                    <p className="text-sm text-muted-foreground">{user.organization}</p>
                    <Badge variant="outline" className="text-xs">
                      {getRoleName(user.role)}
                    </Badge>
                  </div>
                </div>
              </div>
              
              <Button 
                variant="outline" 
                size="sm"
                onClick={logout}
                className="text-muted-foreground hover:text-foreground"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-8">
        {renderDashboard()}
      </main>

      {/* Footer */}
      <footer className="border-t bg-card mt-12">
        <div className="container mx-auto px-6 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-primary p-1.5 rounded">
                <BarChart3 className="h-4 w-4 text-white" />
              </div>
              <div>
                <p className="text-sm font-medium text-foreground">PulseIQ</p>
                <p className="text-xs text-muted-foreground">© 2024 Insurance Regulatory Authority of Uganda</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-6 text-sm text-muted-foreground">
              <a href="#" className="hover:text-foreground">Privacy Policy</a>
              <a href="#" className="hover:text-foreground">Terms of Service</a>
              <a href="#" className="hover:text-foreground">Support</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Dashboard;
