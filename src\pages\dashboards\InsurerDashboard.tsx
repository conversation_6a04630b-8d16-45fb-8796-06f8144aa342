import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  DollarSign, 
  Target,
  Upload,
  BarChart3,
  PieChart,
  FileText,
  Award
} from 'lucide-react';

const InsurerDashboard = () => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Insurer Performance Dashboard</h1>
          <p className="text-muted-foreground">Track your performance and benchmark against industry standards</p>
        </div>
        <div className="flex space-x-3">
          <Badge className="bg-info text-white">Data Current</Badge>
          <Button className="bg-gradient-primary hover:bg-primary-glow text-white">
            <Upload className="h-4 w-4 mr-2" />
            Upload Data
          </Button>
        </div>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="shadow-data-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Gross Premium</CardTitle>
            <DollarSign className="h-4 w-4 text-success" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">UGX 45.2B</div>
            <div className="flex items-center text-sm">
              <TrendingUp className="h-4 w-4 text-success mr-1" />
              <span className="text-success">+8.3%</span>
              <span className="text-muted-foreground ml-1">vs last quarter</span>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-data-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Active Policies</CardTitle>
            <Users className="h-4 w-4 text-info" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">127,543</div>
            <div className="flex items-center text-sm">
              <TrendingUp className="h-4 w-4 text-success mr-1" />
              <span className="text-success">+5.7%</span>
              <span className="text-muted-foreground ml-1">new policies</span>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-data-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Claims Ratio</CardTitle>
            <Target className="h-4 w-4 text-warning" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">62.4%</div>
            <div className="flex items-center text-sm">
              <TrendingDown className="h-4 w-4 text-success mr-1" />
              <span className="text-success">-3.2%</span>
              <span className="text-muted-foreground ml-1">improved</span>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-data-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Market Rank</CardTitle>
            <Award className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">#3</div>
            <div className="flex items-center text-sm">
              <TrendingUp className="h-4 w-4 text-success mr-1" />
              <span className="text-success">+1</span>
              <span className="text-muted-foreground ml-1">position</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance vs Industry */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="shadow-data-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-primary" />
              <span>Performance vs Industry Average</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-foreground">Premium Growth</span>
                <span className="text-foreground font-medium">8.3% vs 6.1%</span>
              </div>
              <Progress value={85} className="h-2" />
              <p className="text-xs text-success">Above industry average</p>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-foreground">Claims Processing Time</span>
                <span className="text-foreground font-medium">12 days vs 18 days</span>
              </div>
              <Progress value={92} className="h-2" />
              <p className="text-xs text-success">Better than average</p>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-foreground">Customer Satisfaction</span>
                <span className="text-foreground font-medium">4.2/5 vs 3.8/5</span>
              </div>
              <Progress value={88} className="h-2" />
              <p className="text-xs text-success">Above industry average</p>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-foreground">Solvency Ratio</span>
                <span className="text-foreground font-medium">245% vs 180%</span>
              </div>
              <Progress value={95} className="h-2" />
              <p className="text-xs text-success">Well above minimum</p>
            </div>
          </CardContent>
        </Card>

        {/* Product Performance */}
        <Card className="shadow-data-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <PieChart className="h-5 w-5 text-info" />
              <span>Product Line Performance</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-3 rounded-lg bg-success/10 border border-success/20">
              <div className="flex-1">
                <p className="font-medium text-foreground">Motor Insurance</p>
                <p className="text-sm text-muted-foreground">UGX 18.5B premium • 45,230 policies</p>
              </div>
              <Badge className="bg-success text-white">+12%</Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 rounded-lg bg-info/10 border border-info/20">
              <div className="flex-1">
                <p className="font-medium text-foreground">Health Insurance</p>
                <p className="text-sm text-muted-foreground">UGX 12.8B premium • 32,150 policies</p>
              </div>
              <Badge className="bg-info text-white">+6%</Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 rounded-lg bg-warning/10 border border-warning/20">
              <div className="flex-1">
                <p className="font-medium text-foreground">Life Insurance</p>
                <p className="text-sm text-muted-foreground">UGX 8.9B premium • 28,890 policies</p>
              </div>
              <Badge className="bg-warning text-white">-2%</Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 rounded-lg bg-primary/10 border border-primary/20">
              <div className="flex-1">
                <p className="font-medium text-foreground">Property Insurance</p>
                <p className="text-sm text-muted-foreground">UGX 5.0B premium • 21,273 policies</p>
              </div>
              <Badge className="bg-primary text-white">+15%</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activities & Data Uploads */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="shadow-data-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-primary" />
              <span>Recent Data Submissions</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-4 p-3 rounded-lg bg-card border">
              <div className="bg-success p-2 rounded-lg">
                <Upload className="h-4 w-4 text-white" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-foreground">Q3 Claims Data</p>
                <p className="text-sm text-muted-foreground">Uploaded successfully • 15,432 records</p>
              </div>
              <Badge className="bg-success text-white">Complete</Badge>
            </div>
            
            <div className="flex items-center space-x-4 p-3 rounded-lg bg-card border">
              <div className="bg-info p-2 rounded-lg">
                <Upload className="h-4 w-4 text-white" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-foreground">Premium Collection</p>
                <p className="text-sm text-muted-foreground">Processing • 8,921 records</p>
              </div>
              <Badge className="bg-info text-white">Processing</Badge>
            </div>
            
            <div className="flex items-center space-x-4 p-3 rounded-lg bg-card border">
              <div className="bg-warning p-2 rounded-lg">
                <Upload className="h-4 w-4 text-white" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-foreground">Policy Renewals</p>
                <p className="text-sm text-muted-foreground">Validation errors • 234 records</p>
              </div>
              <Badge className="bg-warning text-white">Attention</Badge>
            </div>
          </CardContent>
        </Card>

        {/* Benchmarking Insights */}
        <Card className="shadow-data-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-success" />
              <span>Benchmarking Insights</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-3 rounded-lg bg-success/10 border border-success/20">
              <p className="font-medium text-foreground mb-1">Top Performer</p>
              <p className="text-sm text-muted-foreground">Your motor insurance claims processing is 33% faster than industry average</p>
            </div>
            
            <div className="p-3 rounded-lg bg-info/10 border border-info/20">
              <p className="font-medium text-foreground mb-1">Growth Opportunity</p>
              <p className="text-sm text-muted-foreground">Health insurance penetration in Eastern region is 40% below market potential</p>
            </div>
            
            <div className="p-3 rounded-lg bg-warning/10 border border-warning/20">
              <p className="font-medium text-foreground mb-1">Attention Needed</p>
              <p className="text-sm text-muted-foreground">Life insurance lapse rate is 15% higher than industry benchmark</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="shadow-data-card">
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button className="bg-gradient-primary hover:bg-primary-glow text-white">
              <Upload className="h-4 w-4 mr-2" />
              Upload Data
            </Button>
            <Button variant="outline">
              <BarChart3 className="h-4 w-4 mr-2" />
              View Reports
            </Button>
            <Button variant="outline">
              <Target className="h-4 w-4 mr-2" />
              Benchmarking
            </Button>
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              Compliance Check
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default InsurerDashboard;
