import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Shield, Building2, Users, BarChart3, Database, Zap } from "lucide-react";

export const StakeholderSections = () => {
  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
            Empowering Every Stakeholder
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Tailored solutions for regulators, insurers, and the public to drive inclusive insurance growth
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* IRA Portal */}
          <Card className="shadow-elevation hover:shadow-lg transition-all duration-300 border-l-4 border-l-primary">
            <CardHeader>
              <div className="flex items-center space-x-3 mb-4">
                <div className="bg-gradient-primary p-3 rounded-lg">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <div>
                  <CardTitle className="text-foreground">IRA Regulatory Portal</CardTitle>
                  <p className="text-sm text-muted-foreground">Enhanced supervision & control</p>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <BarChart3 className="h-4 w-4 text-primary" />
                  <span className="text-sm text-foreground">Real-time market monitoring</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Database className="h-4 w-4 text-primary" />
                  <span className="text-sm text-foreground">Regulatory sandbox management</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Zap className="h-4 w-4 text-primary" />
                  <span className="text-sm text-foreground">Early warning systems</span>
                </div>
              </div>
              
              <Button className="w-full bg-gradient-primary hover:bg-primary-glow text-white">
                Access IRA Portal
              </Button>
            </CardContent>
          </Card>

          {/* Insurers Portal */}
          <Card className="shadow-elevation hover:shadow-lg transition-all duration-300 border-l-4 border-l-info">
            <CardHeader>
              <div className="flex items-center space-x-3 mb-4">
                <div className="bg-info p-3 rounded-lg">
                  <Building2 className="h-6 w-6 text-white" />
                </div>
                <div>
                  <CardTitle className="text-foreground">Insurers Platform</CardTitle>
                  <p className="text-sm text-muted-foreground">Operational efficiency & insights</p>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <BarChart3 className="h-4 w-4 text-info" />
                  <span className="text-sm text-foreground">Performance benchmarking</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Database className="h-4 w-4 text-info" />
                  <span className="text-sm text-foreground">Secure data pooling</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Zap className="h-4 w-4 text-info" />
                  <span className="text-sm text-foreground">Fast product approvals</span>
                </div>
              </div>
              
              <Button variant="outline" className="w-full border-info text-info hover:bg-info hover:text-white">
                Insurer Access
              </Button>
            </CardContent>
          </Card>

          {/* Public Portal */}
          <Card className="shadow-elevation hover:shadow-lg transition-all duration-300 border-l-4 border-l-success">
            <CardHeader>
              <div className="flex items-center space-x-3 mb-4">
                <div className="bg-success p-3 rounded-lg">
                  <Users className="h-6 w-6 text-white" />
                </div>
                <div>
                  <CardTitle className="text-foreground">Public Access</CardTitle>
                  <p className="text-sm text-muted-foreground">Transparency & verification</p>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Shield className="h-4 w-4 text-success" />
                  <span className="text-sm text-foreground">Policy verification tools</span>
                </div>
                <div className="flex items-center space-x-3">
                  <BarChart3 className="h-4 w-4 text-success" />
                  <span className="text-sm text-foreground">Market transparency data</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Database className="h-4 w-4 text-success" />
                  <span className="text-sm text-foreground">Fraud reduction support</span>
                </div>
              </div>
              
              <Button variant="outline" className="w-full border-success text-success hover:bg-success hover:text-white">
                Public Portal
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Implementation Phases */}
        <div className="mt-20">
          <h3 className="text-2xl font-bold text-center mb-12 text-foreground">
            Phased Implementation Roadmap
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="bg-gradient-primary w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold">1-3</span>
              </div>
              <h4 className="font-semibold text-foreground mb-2">Foundation Phase</h4>
              <p className="text-sm text-muted-foreground">Core dashboards, data uploads, public verification</p>
            </div>
            
            <div className="text-center">
              <div className="bg-info w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold">4-6</span>
              </div>
              <h4 className="font-semibold text-foreground mb-2">Integration Phase</h4>
              <p className="text-sm text-muted-foreground">Data warehouse, predictive analytics, risk insights</p>
            </div>
            
            <div className="text-center">
              <div className="bg-success w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold">7+</span>
              </div>
              <h4 className="font-semibold text-foreground mb-2">Expansion Phase</h4>
              <p className="text-sm text-muted-foreground">Sandbox testing, API licensing, sustainable revenue</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};