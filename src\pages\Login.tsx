import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { BarChart3, Shield, Building2, Users, Eye, EyeOff } from 'lucide-react';
import { mockUsers } from '@/lib/utils';

const Login = () => {
  const { login, isLoading } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [selectedRole, setSelectedRole] = useState<'ira' | 'insurer' | 'public' | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!email || !password) {
      setError('Please enter both email and password');
      return;
    }

    const success = await login(email, password);
    if (!success) {
      setError('Invalid email or password');
    }
  };

  const handleQuickLogin = (userEmail: string) => {
    setEmail(userEmail);
    setPassword('password123');
    setError('');
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'ira':
        return <Shield className="h-5 w-5" />;
      case 'insurer':
        return <Building2 className="h-5 w-5" />;
      case 'public':
        return <Users className="h-5 w-5" />;
      default:
        return null;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ira':
        return 'bg-primary text-primary-foreground';
      case 'insurer':
        return 'bg-info text-white';
      case 'public':
        return 'bg-success text-white';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-secondary flex items-center justify-center p-6">
      <div className="w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Side - Branding */}
        <div className="flex flex-col justify-center space-y-8">
          <div className="text-center lg:text-left">
            <div className="flex items-center justify-center lg:justify-start space-x-3 mb-6">
              <div className="bg-gradient-primary p-3 rounded-lg">
                <BarChart3 className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-primary">PulseIQ</h1>
                <p className="text-muted-foreground">Data Intelligence Hub</p>
              </div>
            </div>
            
            <h2 className="text-2xl md:text-3xl font-bold text-foreground mb-4">
              Welcome to Uganda's Insurance Intelligence Platform
            </h2>
            
            <p className="text-lg text-muted-foreground mb-8">
              Centralizing multi-sectoral data to drive evidence-based regulation, 
              product innovation, and market transparency.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 rounded-lg bg-card shadow-data-card">
                <Shield className="h-8 w-8 text-primary mx-auto mb-2" />
                <h3 className="font-semibold text-foreground">IRA Portal</h3>
                <p className="text-sm text-muted-foreground">Regulatory oversight</p>
              </div>
              
              <div className="text-center p-4 rounded-lg bg-card shadow-data-card">
                <Building2 className="h-8 w-8 text-info mx-auto mb-2" />
                <h3 className="font-semibold text-foreground">Insurers</h3>
                <p className="text-sm text-muted-foreground">Performance insights</p>
              </div>
              
              <div className="text-center p-4 rounded-lg bg-card shadow-data-card">
                <Users className="h-8 w-8 text-success mx-auto mb-2" />
                <h3 className="font-semibold text-foreground">Public Access</h3>
                <p className="text-sm text-muted-foreground">Transparency tools</p>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Login Form */}
        <div className="flex flex-col justify-center">
          <Card className="shadow-elevation">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl text-foreground">Sign In</CardTitle>
              <CardDescription>
                Enter your credentials to access the platform
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {error && (
                <Alert className="border-destructive">
                  <AlertDescription className="text-destructive">
                    {error}
                  </AlertDescription>
                </Alert>
              )}

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={isLoading}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      placeholder="Enter your password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      disabled={isLoading}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                <Button 
                  type="submit" 
                  className="w-full bg-gradient-primary hover:bg-primary-glow text-white"
                  disabled={isLoading}
                >
                  {isLoading ? 'Signing in...' : 'Sign In'}
                </Button>
              </form>

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-card px-2 text-muted-foreground">Demo Accounts</span>
                </div>
              </div>

              <div className="space-y-3">
                {mockUsers.map((user) => (
                  <Button
                    key={user.id}
                    variant="outline"
                    className="w-full justify-start h-auto p-3 hover:bg-muted/50 hover:border-primary/20 transition-colors group"
                    onClick={() => handleQuickLogin(user.email)}
                    disabled={isLoading}
                  >
                    <div className="flex items-center space-x-3 w-full">
                      <div className={`p-2 rounded-lg ${getRoleColor(user.role)}`}>
                        {getRoleIcon(user.role)}
                      </div>
                      <div className="flex-1 text-left">
                        <div className="font-medium text-foreground group-hover:text-foreground">{user.name}</div>
                        <div className="text-sm text-muted-foreground group-hover:text-muted-foreground">{user.organization}</div>
                      </div>
                      <Badge variant="outline" className="text-xs group-hover:border-primary/30">
                        {user.role.toUpperCase()}
                      </Badge>
                    </div>
                  </Button>
                ))}
              </div>

              <div className="text-center text-sm text-muted-foreground">
                Demo password: <code className="bg-muted px-1 rounded">password123</code>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Login;
