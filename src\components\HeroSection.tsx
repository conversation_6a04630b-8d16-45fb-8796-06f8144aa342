import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, Shield, Zap, Users } from "lucide-react";
import heroImage from "@/assets/pulseiq-hero.jpg";

export const HeroSection = () => {
  return (
    <section className="relative min-h-screen flex items-center">
      {/* Background Image with Overlay */}
      <div 
        className="absolute inset-0 bg-cover bg-center"
        style={{ backgroundImage: `url(${heroImage})` }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-background/95 via-background/80 to-background/60" />
      </div>
      
      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-3xl animate-fade-in">
          <Badge className="mb-6 bg-primary-glow/10 text-primary border-primary/20">
            Uganda Insurance Regulatory Authority
          </Badge>
          
          <h1 className="text-4xl md:text-6xl font-bold mb-6 text-foreground">
            Data Intelligence for
            <span className="block bg-gradient-primary bg-clip-text text-transparent">
              Inclusive Insurance Growth
            </span>
          </h1>
          
          <p className="text-xl text-muted-foreground mb-8 leading-relaxed max-w-2xl">
            PulseIQ centralizes fragmented multi-sectoral data to drive evidence-based regulation, 
            product innovation, and market transparency across Uganda's insurance ecosystem.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 mb-12">
            <Button size="lg" className="bg-gradient-primary hover:bg-primary-glow text-white text-lg px-8">
              Explore Platform
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8">
              View Demo
            </Button>
          </div>

          {/* Key Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-center space-x-3 p-4 rounded-lg bg-card shadow-data-card">
              <div className="bg-gradient-primary p-2 rounded-lg">
                <TrendingUp className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-foreground">Real-time Analytics</h3>
                <p className="text-sm text-muted-foreground">Dynamic monitoring & insights</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 p-4 rounded-lg bg-card shadow-data-card">
              <div className="bg-gradient-primary p-2 rounded-lg">
                <Shield className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-foreground">Regulatory Sandbox</h3>
                <p className="text-sm text-muted-foreground">Innovation testing platform</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 p-4 rounded-lg bg-card shadow-data-card">
              <div className="bg-gradient-primary p-2 rounded-lg">
                <Users className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-foreground">Data Collaboration</h3>
                <p className="text-sm text-muted-foreground">Secure benchmarking & pooling</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};