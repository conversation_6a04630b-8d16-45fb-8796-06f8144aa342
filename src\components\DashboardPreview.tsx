import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { TrendingUp, TrendingDown, Users, Shield, DollarSign, AlertTriangle } from "lucide-react";

export const DashboardPreview = () => {
  return (
    <section className="py-20 bg-gradient-secondary">
      <div className="container mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
            Comprehensive Insurance Intelligence
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Real-time dashboards providing actionable insights for regulators, insurers, and stakeholders
          </p>
        </div>

        {/* KPI Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <Card className="shadow-data-card hover:shadow-elevation transition-shadow duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Total Premium</CardTitle>
              <DollarSign className="h-4 w-4 text-success" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">UGX 847.2B</div>
              <div className="flex items-center text-sm">
                <TrendingUp className="h-4 w-4 text-success mr-1" />
                <span className="text-success">+12.5%</span>
                <span className="text-muted-foreground ml-1">vs last quarter</span>
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-data-card hover:shadow-elevation transition-shadow duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Active Policies</CardTitle>
              <Users className="h-4 w-4 text-info" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">2.3M</div>
              <div className="flex items-center text-sm">
                <TrendingUp className="h-4 w-4 text-success mr-1" />
                <span className="text-success">+8.3%</span>
                <span className="text-muted-foreground ml-1">penetration rate</span>
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-data-card hover:shadow-elevation transition-shadow duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Claims Ratio</CardTitle>
              <Shield className="h-4 w-4 text-warning" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">67.4%</div>
              <div className="flex items-center text-sm">
                <TrendingDown className="h-4 w-4 text-success mr-1" />
                <span className="text-success">-2.1%</span>
                <span className="text-muted-foreground ml-1">improved efficiency</span>
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-data-card hover:shadow-elevation transition-shadow duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Regulatory Score</CardTitle>
              <AlertTriangle className="h-4 w-4 text-success" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">94.2</div>
              <div className="flex items-center text-sm">
                <TrendingUp className="h-4 w-4 text-success mr-1" />
                <span className="text-success">+1.8%</span>
                <span className="text-muted-foreground ml-1">compliance rate</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Regional Performance */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Card className="shadow-data-card">
            <CardHeader>
              <CardTitle className="text-foreground">Regional Market Penetration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-foreground">Central Region</span>
                  <span className="text-foreground font-medium">23.4%</span>
                </div>
                <Progress value={78} className="h-2" />
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-foreground">Western Region</span>
                  <span className="text-foreground font-medium">18.7%</span>
                </div>
                <Progress value={62} className="h-2" />
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-foreground">Eastern Region</span>
                  <span className="text-foreground font-medium">15.2%</span>
                </div>
                <Progress value={51} className="h-2" />
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-foreground">Northern Region</span>
                  <span className="text-foreground font-medium">12.9%</span>
                </div>
                <Progress value={43} className="h-2" />
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-data-card">
            <CardHeader>
              <CardTitle className="text-foreground">Product Innovation Pipeline</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-3 rounded-lg bg-success/10 border border-success/20">
                <div>
                  <p className="font-medium text-foreground">Parametric Crop Insurance</p>
                  <p className="text-sm text-muted-foreground">Pilot Phase - 3 months</p>
                </div>
                <Badge className="bg-success text-white">Active</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 rounded-lg bg-info/10 border border-info/20">
                <div>
                  <p className="font-medium text-foreground">Micro Health Coverage</p>
                  <p className="text-sm text-muted-foreground">Testing Phase - 6 weeks</p>
                </div>
                <Badge className="bg-info text-white">Testing</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 rounded-lg bg-warning/10 border border-warning/20">
                <div>
                  <p className="font-medium text-foreground">Digital Life Insurance</p>
                  <p className="text-sm text-muted-foreground">Review Phase - 2 weeks</p>
                </div>
                <Badge className="bg-warning text-white">Review</Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};