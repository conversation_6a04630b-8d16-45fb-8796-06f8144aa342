import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Mock user data for prototype
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'ira' | 'insurer' | 'public';
  organization?: string;
  avatar?: string;
}

export const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'ira',
    organization: 'Insurance Regulatory Authority',
    avatar: '/avatars/sarah.jpg'
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'insurer',
    organization: 'Jubilee Insurance',
    avatar: '/avatars/david.jpg'
  },
  {
    id: '3',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'insurer',
    organization: 'NSSF',
    avatar: '/avatars/grace.jpg'
  },
  {
    id: '4',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'public',
    organization: 'Public User',
    avatar: '/avatars/john.jpg'
  }
];

export const authenticateUser = (email: string, password: string): User | null => {
  // Mock authentication - in real app this would be an API call
  const user = mockUsers.find(u => u.email === email);
  if (user && password === 'password123') {
    return user;
  }
  return null;
};
