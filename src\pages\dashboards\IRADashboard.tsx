import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Shield, 
  DollarSign, 
  AlertTriangle,
  FileText,
  BarChart3,
  Activity,
  CheckCircle
} from 'lucide-react';

const IRADashboard = () => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">IRA Regulatory Dashboard</h1>
          <p className="text-muted-foreground">Real-time market oversight and regulatory intelligence</p>
        </div>
        <div className="flex space-x-3">
          <Badge className="bg-success text-white">Market Open</Badge>
          <Badge variant="outline">Last Updated: 2 min ago</Badge>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="shadow-data-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Market Premium</CardTitle>
            <DollarSign className="h-4 w-4 text-success" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">UGX 847.2B</div>
            <div className="flex items-center text-sm">
              <TrendingUp className="h-4 w-4 text-success mr-1" />
              <span className="text-success">+12.5%</span>
              <span className="text-muted-foreground ml-1">vs last quarter</span>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-data-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Licensed Insurers</CardTitle>
            <Shield className="h-4 w-4 text-info" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">47</div>
            <div className="flex items-center text-sm">
              <TrendingUp className="h-4 w-4 text-success mr-1" />
              <span className="text-success">+3</span>
              <span className="text-muted-foreground ml-1">new licenses</span>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-data-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Compliance Score</CardTitle>
            <CheckCircle className="h-4 w-4 text-success" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">94.2%</div>
            <div className="flex items-center text-sm">
              <TrendingUp className="h-4 w-4 text-success mr-1" />
              <span className="text-success">+1.8%</span>
              <span className="text-muted-foreground ml-1">improvement</span>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-data-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Active Alerts</CardTitle>
            <AlertTriangle className="h-4 w-4 text-warning" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">7</div>
            <div className="flex items-center text-sm">
              <TrendingDown className="h-4 w-4 text-success mr-1" />
              <span className="text-success">-3</span>
              <span className="text-muted-foreground ml-1">from yesterday</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Market Penetration by Region */}
        <Card className="shadow-data-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-primary" />
              <span>Regional Market Penetration</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-foreground">Central Region</span>
                <span className="text-foreground font-medium">23.4%</span>
              </div>
              <Progress value={78} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-foreground">Western Region</span>
                <span className="text-foreground font-medium">18.7%</span>
              </div>
              <Progress value={62} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-foreground">Eastern Region</span>
                <span className="text-foreground font-medium">15.2%</span>
              </div>
              <Progress value={51} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-foreground">Northern Region</span>
                <span className="text-foreground font-medium">12.9%</span>
              </div>
              <Progress value={43} className="h-2" />
            </div>
          </CardContent>
        </Card>

        {/* Regulatory Alerts */}
        <Card className="shadow-data-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-warning" />
              <span>Priority Alerts</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-3 rounded-lg bg-destructive/10 border border-destructive/20">
              <div className="flex-1">
                <p className="font-medium text-foreground">Capital Adequacy Alert</p>
                <p className="text-sm text-muted-foreground">ABC Insurance - Below minimum threshold</p>
              </div>
              <Badge className="bg-destructive text-white">High</Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 rounded-lg bg-warning/10 border border-warning/20">
              <div className="flex-1">
                <p className="font-medium text-foreground">Late Filing</p>
                <p className="text-sm text-muted-foreground">XYZ Life - Q3 returns overdue</p>
              </div>
              <Badge className="bg-warning text-white">Medium</Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 rounded-lg bg-info/10 border border-info/20">
              <div className="flex-1">
                <p className="font-medium text-foreground">New Product Application</p>
                <p className="text-sm text-muted-foreground">DEF General - Parametric crop insurance</p>
              </div>
              <Badge className="bg-info text-white">Review</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activities */}
      <Card className="shadow-data-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5 text-primary" />
            <span>Recent Regulatory Activities</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-4 p-3 rounded-lg bg-card border">
              <div className="bg-success p-2 rounded-lg">
                <CheckCircle className="h-4 w-4 text-white" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-foreground">License Approved</p>
                <p className="text-sm text-muted-foreground">New microinsurance license issued to MicroCover Ltd</p>
              </div>
              <span className="text-sm text-muted-foreground">2 hours ago</span>
            </div>
            
            <div className="flex items-center space-x-4 p-3 rounded-lg bg-card border">
              <div className="bg-info p-2 rounded-lg">
                <FileText className="h-4 w-4 text-white" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-foreground">Compliance Report Generated</p>
                <p className="text-sm text-muted-foreground">Q3 2024 market compliance summary completed</p>
              </div>
              <span className="text-sm text-muted-foreground">5 hours ago</span>
            </div>
            
            <div className="flex items-center space-x-4 p-3 rounded-lg bg-card border">
              <div className="bg-warning p-2 rounded-lg">
                <AlertTriangle className="h-4 w-4 text-white" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-foreground">Investigation Initiated</p>
                <p className="text-sm text-muted-foreground">Consumer complaint escalated for formal review</p>
              </div>
              <span className="text-sm text-muted-foreground">1 day ago</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card className="shadow-data-card">
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button className="bg-gradient-primary hover:bg-primary-glow text-white">
              Generate Report
            </Button>
            <Button variant="outline">
              Review Applications
            </Button>
            <Button variant="outline">
              Market Analysis
            </Button>
            <Button variant="outline">
              Sandbox Management
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default IRADashboard;
