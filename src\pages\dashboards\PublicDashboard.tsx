import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Search, 
  Shield, 
  TrendingUp, 
  Users, 
  DollarSign, 
  CheckCircle,
  AlertCircle,
  Info,
  Star,
  MapPin,
  Phone
} from 'lucide-react';

const PublicDashboard = () => {
  const [policyNumber, setPolicyNumber] = useState('');
  const [searchResult, setSearchResult] = useState<any>(null);

  const handlePolicySearch = () => {
    // Mock policy verification
    if (policyNumber.trim()) {
      setSearchResult({
        policyNumber: policyNumber,
        status: 'Active',
        insurer: 'Jubilee Insurance',
        type: 'Motor Insurance',
        premium: 'UGX 850,000',
        expiryDate: '2024-12-15',
        isValid: true
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-foreground mb-2">Public Insurance Portal</h1>
        <p className="text-muted-foreground">Verify policies, explore market data, and make informed insurance decisions</p>
      </div>

      {/* Policy Verification Section */}
      <Card className="shadow-data-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5 text-primary" />
            <span>Policy Verification</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-4">
            <div className="flex-1">
              <Label htmlFor="policy-number">Policy Number</Label>
              <Input
                id="policy-number"
                placeholder="Enter your policy number (e.g., POL-2024-001234)"
                value={policyNumber}
                onChange={(e) => setPolicyNumber(e.target.value)}
              />
            </div>
            <Button 
              onClick={handlePolicySearch}
              className="bg-gradient-primary hover:bg-primary-glow text-white mt-6"
            >
              <Search className="h-4 w-4 mr-2" />
              Verify Policy
            </Button>
          </div>

          {searchResult && (
            <div className="mt-6 p-4 rounded-lg bg-success/10 border border-success/20">
              <div className="flex items-center space-x-2 mb-3">
                <CheckCircle className="h-5 w-5 text-success" />
                <span className="font-medium text-success">Policy Verified</span>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Policy Number:</p>
                  <p className="font-medium text-foreground">{searchResult.policyNumber}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Status:</p>
                  <Badge className="bg-success text-white">{searchResult.status}</Badge>
                </div>
                <div>
                  <p className="text-muted-foreground">Insurer:</p>
                  <p className="font-medium text-foreground">{searchResult.insurer}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Type:</p>
                  <p className="font-medium text-foreground">{searchResult.type}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Annual Premium:</p>
                  <p className="font-medium text-foreground">{searchResult.premium}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Expiry Date:</p>
                  <p className="font-medium text-foreground">{searchResult.expiryDate}</p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Market Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="shadow-data-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Licensed Insurers</CardTitle>
            <Shield className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">47</div>
            <p className="text-xs text-muted-foreground">Active insurance companies</p>
          </CardContent>
        </Card>

        <Card className="shadow-data-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Market Penetration</CardTitle>
            <Users className="h-4 w-4 text-info" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">18.7%</div>
            <p className="text-xs text-muted-foreground">Population with insurance</p>
          </CardContent>
        </Card>

        <Card className="shadow-data-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Average Premium</CardTitle>
            <DollarSign className="h-4 w-4 text-success" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">UGX 420K</div>
            <p className="text-xs text-muted-foreground">Annual motor insurance</p>
          </CardContent>
        </Card>

        <Card className="shadow-data-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Claims Paid</CardTitle>
            <TrendingUp className="h-4 w-4 text-success" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">94.2%</div>
            <p className="text-xs text-muted-foreground">Claims settlement ratio</p>
          </CardContent>
        </Card>
      </div>

      {/* Top Rated Insurers */}
      <Card className="shadow-data-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Star className="h-5 w-5 text-warning" />
            <span>Top Rated Insurance Companies</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 rounded-lg bg-card border">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-foreground">Jubilee Insurance</h3>
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 text-warning fill-current" />
                  <span className="text-sm font-medium">4.8</span>
                </div>
              </div>
              <p className="text-sm text-muted-foreground mb-2">Motor, Health, Life Insurance</p>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <MapPin className="h-3 w-3" />
                <span>15 branches nationwide</span>
              </div>
            </div>

            <div className="p-4 rounded-lg bg-card border">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-foreground">UAP Insurance</h3>
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 text-warning fill-current" />
                  <span className="text-sm font-medium">4.6</span>
                </div>
              </div>
              <p className="text-sm text-muted-foreground mb-2">General Insurance, Bonds</p>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <MapPin className="h-3 w-3" />
                <span>12 branches nationwide</span>
              </div>
            </div>

            <div className="p-4 rounded-lg bg-card border">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-foreground">NSSF</h3>
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 text-warning fill-current" />
                  <span className="text-sm font-medium">4.5</span>
                </div>
              </div>
              <p className="text-sm text-muted-foreground mb-2">Social Security, Pensions</p>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <MapPin className="h-3 w-3" />
                <span>8 regional offices</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Insurance Education */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="shadow-data-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Info className="h-5 w-5 text-info" />
              <span>Understanding Insurance</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-3 rounded-lg bg-info/10 border border-info/20">
              <h4 className="font-medium text-foreground mb-1">What is Insurance?</h4>
              <p className="text-sm text-muted-foreground">Insurance is a contract that provides financial protection against unexpected losses in exchange for regular premium payments.</p>
            </div>
            
            <div className="p-3 rounded-lg bg-success/10 border border-success/20">
              <h4 className="font-medium text-foreground mb-1">Why Do You Need It?</h4>
              <p className="text-sm text-muted-foreground">Insurance protects you and your family from financial hardship due to accidents, illness, or property damage.</p>
            </div>
            
            <div className="p-3 rounded-lg bg-warning/10 border border-warning/20">
              <h4 className="font-medium text-foreground mb-1">Types Available</h4>
              <p className="text-sm text-muted-foreground">Motor, Health, Life, Property, and Agricultural insurance products are available in Uganda.</p>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-data-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-warning" />
              <span>Consumer Protection</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-3 rounded-lg bg-card border">
              <h4 className="font-medium text-foreground mb-1">File a Complaint</h4>
              <p className="text-sm text-muted-foreground mb-2">Having issues with your insurer? Report to IRA for assistance.</p>
              <Button variant="outline" size="sm">
                <Phone className="h-4 w-4 mr-2" />
                Contact IRA
              </Button>
            </div>
            
            <div className="p-3 rounded-lg bg-card border">
              <h4 className="font-medium text-foreground mb-1">Know Your Rights</h4>
              <p className="text-sm text-muted-foreground mb-2">Learn about your rights as an insurance consumer in Uganda.</p>
              <Button variant="outline" size="sm">
                <Info className="h-4 w-4 mr-2" />
                Learn More
              </Button>
            </div>
            
            <div className="p-3 rounded-lg bg-card border">
              <h4 className="font-medium text-foreground mb-1">Fraud Prevention</h4>
              <p className="text-sm text-muted-foreground mb-2">Tips to avoid insurance fraud and protect yourself.</p>
              <Button variant="outline" size="sm">
                <Shield className="h-4 w-4 mr-2" />
                Safety Tips
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="shadow-data-card">
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button className="bg-gradient-primary hover:bg-primary-glow text-white">
              <Search className="h-4 w-4 mr-2" />
              Find Insurer
            </Button>
            <Button variant="outline">
              <Info className="h-4 w-4 mr-2" />
              Compare Policies
            </Button>
            <Button variant="outline">
              <AlertCircle className="h-4 w-4 mr-2" />
              Report Issue
            </Button>
            <Button variant="outline">
              <Phone className="h-4 w-4 mr-2" />
              Get Help
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PublicDashboard;
